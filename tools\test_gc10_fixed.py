"""
测试修复后的GC10训练配置
"""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

import argparse
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

import src.misc.dist as dist_utils
from src.core import YAMLConfig
from src.solver import TASKS


def test_model_creation(config_path):
    """测试模型创建是否成功"""
    print("=" * 60)
    print("测试模型创建...")
    print("=" * 60)
    
    try:
        # 加载配置
        cfg = YAMLConfig(config_path, resume=None, use_amp=True, tuning=None)
        print("✓ 配置文件加载成功")
        
        # 创建求解器
        solver = TASKS[cfg.yaml_cfg['task']](cfg)
        print("✓ 求解器创建成功")
        
        # 打印模型信息
        print(f"模型类型: {cfg.yaml_cfg.get('model', 'Unknown')}")
        print(f"损失函数: {cfg.yaml_cfg.get('criterion', 'Unknown')}")
        print(f"类别数量: {cfg.yaml_cfg.get('RTDETRTransformer', {}).get('num_classes', 'Unknown')}")
        print(f"批次大小: {cfg.yaml_cfg.get('batch_size', 'Unknown')}")
        print(f"学习率: {cfg.yaml_cfg.get('optimizer', {}).get('lr', 'Unknown')}")
        
        # 测试模型前向传播
        print("\n测试模型前向传播...")
        model = solver.model
        model.eval()
        
        # 创建测试输入
        batch_size = 1
        test_input = torch.randn(batch_size, 3, 320, 320)
        
        with torch.no_grad():
            outputs = model(test_input)
            print("✓ 模型前向传播成功")
            print(f"输出形状: {outputs['pred_logits'].shape if 'pred_logits' in outputs else 'Unknown'}")
            print(f"预测框形状: {outputs['pred_boxes'].shape if 'pred_boxes' in outputs else 'Unknown'}")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_loss_computation(config_path):
    """测试损失函数计算"""
    print("\n" + "=" * 60)
    print("测试损失函数计算...")
    print("=" * 60)
    
    try:
        # 加载配置
        cfg = YAMLConfig(config_path, resume=None, use_amp=True, tuning=None)
        solver = TASKS[cfg.yaml_cfg['task']](cfg)
        
        model = solver.model
        criterion = solver.criterion
        model.train()
        criterion.train()
        
        # 创建测试数据
        batch_size = 2
        test_input = torch.randn(batch_size, 3, 320, 320)
        
        # 创建测试目标
        targets = []
        for i in range(batch_size):
            target = {
                'boxes': torch.tensor([[0.5, 0.5, 0.2, 0.2], [0.3, 0.3, 0.1, 0.1]], dtype=torch.float32),
                'labels': torch.tensor([1, 2], dtype=torch.int64),
                'image_id': torch.tensor(i),
                'area': torch.tensor([0.04, 0.01], dtype=torch.float32),
                'iscrowd': torch.tensor([0, 0], dtype=torch.int64),
                'orig_size': torch.tensor([320, 320], dtype=torch.int64),
                'size': torch.tensor([320, 320], dtype=torch.int64)
            }
            targets.append(target)
        
        # 前向传播
        outputs = model(test_input, targets)
        print("✓ 模型前向传播成功")
        
        # 计算损失
        loss_dict = criterion(outputs, targets)
        print("✓ 损失函数计算成功")
        
        # 打印损失信息
        total_loss = sum(loss_dict.values())
        print(f"总损失: {total_loss.item():.4f}")
        for k, v in loss_dict.items():
            print(f"  {k}: {v.item():.4f}")
        
        # 测试反向传播
        total_loss.backward()
        print("✓ 反向传播成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    parser = argparse.ArgumentParser(description='测试修复后的GC10配置')
    parser.add_argument('--config', '-c', type=str, 
                       default='configs/rtdetr/rtdetr_relan_gc10.yml',
                       help='配置文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        return
    
    print("开始测试修复后的GC10配置...")
    print(f"配置文件: {args.config}")
    
    # 测试模型创建
    success1 = test_model_creation(args.config)
    
    # 测试损失函数计算
    success2 = test_loss_computation(args.config)
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    print(f"模型创建: {'✓ 成功' if success1 else '✗ 失败'}")
    print(f"损失计算: {'✓ 成功' if success2 else '✗ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！配置修复成功，可以开始训练。")
        print("\n建议的训练命令:")
        print(f"python tools/train_gc10.py --config {args.config}")
    else:
        print("\n❌ 测试失败，需要进一步修复配置。")


if __name__ == '__main__':
    main()
